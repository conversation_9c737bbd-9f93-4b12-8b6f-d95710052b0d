#!/usr/bin/env python3
"""
Debug script for face recognition issues
This script helps diagnose problems with face detection and recognition
"""

import sys
import os
import cv2
import numpy as np
from pathlib import Path

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_face_detection():
    """Test face detection on a sample image"""
    print("=" * 60)
    print("TESTING FACE DETECTION")
    print("=" * 60)
    
    try:
        from app.services.face_utils_mediapipe import mediapipe_face_recognition_system
        
        # Create a test image with a simple face-like pattern
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Draw a simple face-like pattern
        cv2.circle(test_image, (320, 240), 100, (255, 255, 255), -1)  # Face
        cv2.circle(test_image, (290, 210), 15, (0, 0, 0), -1)  # Left eye
        cv2.circle(test_image, (350, 210), 15, (0, 0, 0), -1)  # Right eye
        cv2.ellipse(test_image, (320, 270), (30, 15), 0, 0, 180, (0, 0, 0), 2)  # Mouth
        
        print("Testing with synthetic face image...")
        faces = mediapipe_face_recognition_system.detect_faces_mediapipe(test_image)
        print(f"Detected {len(faces)} faces in synthetic image")
        
        # Test with webcam if available
        print("\nTesting with webcam...")
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                faces = mediapipe_face_recognition_system.detect_faces_mediapipe(frame)
                print(f"Detected {len(faces)} faces in webcam frame")
                
                # Show detection details
                for i, (left, top, right, bottom) in enumerate(faces):
                    width = right - left
                    height = bottom - top
                    print(f"  Face {i+1}: position=({left},{top},{right},{bottom}), size={width}x{height}")
            cap.release()
        else:
            print("Webcam not available")
            
    except Exception as e:
        print(f"Face detection test failed: {e}")
        import traceback
        traceback.print_exc()

def test_face_recognition():
    """Test face recognition with registered users"""
    print("\n" + "=" * 60)
    print("TESTING FACE RECOGNITION")
    print("=" * 60)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get registered users
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.is_active == True).all()
        
        print(f"Found {len(users)} registered users:")
        for user in users:
            print(f"  - {user.name} (ID: {user.id})")
        
        if len(users) == 0:
            print("No registered users found. Please register a user first.")
            return
        
        # Load known faces
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
        
        face_recognition_system.load_known_faces(users_data)
        
        # Get performance stats
        stats = face_recognition_system.get_performance_stats()
        print(f"\nRecognition system: {stats['recognition_system']}")
        print(f"Registered faces loaded: {stats['registered_faces']}")
        print(f"Similarity threshold: {stats['similarity_threshold']}")
        
        # Test with webcam
        print("\nTesting recognition with webcam...")
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("Press 'q' to quit, 's' to save debug frame")
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                
                # Run recognition
                results = face_recognition_system.recognize_faces_in_frame(frame)
                
                # Draw results
                frame_with_boxes = face_recognition_system.draw_face_boxes(frame, results)
                
                # Add debug info
                cv2.putText(frame_with_boxes, f"Frame: {frame_count}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame_with_boxes, f"Faces: {len(results)}", (10, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # Show recognition details
                for i, result in enumerate(results):
                    name = result['name']
                    confidence = result['confidence']
                    is_known = result['is_known']
                    print(f"Frame {frame_count}: Face {i+1} - {name} (confidence: {confidence:.3f}, known: {is_known})")
                
                cv2.imshow('Face Recognition Debug', frame_with_boxes)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    debug_filename = f"debug_frame_{frame_count}.jpg"
                    cv2.imwrite(debug_filename, frame_with_boxes)
                    print(f"Saved debug frame: {debug_filename}")
            
            cap.release()
            cv2.destroyAllWindows()
        else:
            print("Webcam not available")
        
        db.close()
        
    except Exception as e:
        print(f"Face recognition test failed: {e}")
        import traceback
        traceback.print_exc()

def test_encoding_compatibility():
    """Test encoding compatibility between different systems"""
    print("\n" + "=" * 60)
    print("TESTING ENCODING COMPATIBILITY")
    print("=" * 60)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.is_active == True).all()
        
        for user in users:
            if user.face_encoding:
                print(f"\nUser: {user.name}")
                
                # Try different encoding formats
                try:
                    # Float32 format
                    encoding_f32 = np.frombuffer(user.face_encoding, dtype=np.float32)
                    print(f"  Float32: {len(encoding_f32)} dimensions")
                    
                    # Float64 format
                    encoding_f64 = np.frombuffer(user.face_encoding, dtype=np.float64)
                    print(f"  Float64: {len(encoding_f64)} dimensions")
                    
                    # Determine likely format
                    if len(encoding_f32) == 128:
                        print(f"  -> Likely MobileFaceNet format (128D float32)")
                    elif len(encoding_f32) == 512:
                        print(f"  -> Likely ArcFace format (512D float32)")
                    elif len(encoding_f64) == 128:
                        print(f"  -> Likely face_recognition format (128D float64)")
                    else:
                        print(f"  -> Unknown format")
                        
                except Exception as e:
                    print(f"  Error reading encoding: {e}")
        
        db.close()
        
    except Exception as e:
        print(f"Encoding compatibility test failed: {e}")

def main():
    """Run all debug tests"""
    print("Face Recognition Debug Tool")
    print("This tool helps diagnose face detection and recognition issues")
    
    # Test face detection
    test_face_detection()
    
    # Test face recognition
    test_face_recognition()
    
    # Test encoding compatibility
    test_encoding_compatibility()
    
    print("\n" + "=" * 60)
    print("DEBUG COMPLETE")
    print("=" * 60)
    print("\nIf you're having issues:")
    print("1. Check that faces are being detected (should see bounding boxes)")
    print("2. Verify registered users have compatible encodings")
    print("3. Try adjusting similarity threshold if recognition is too strict")
    print("4. Consider re-registering users for best compatibility")

if __name__ == "__main__":
    main()
