#!/usr/bin/env python3
"""
Reset system to pure MobileFaceNet - clear all old ArcFace encodings
This script will clear all face encodings and require fresh registration
"""

import sys
import os

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def reset_to_mobilefacenet():
    """Clear all face encodings to force fresh MobileFaceNet registration"""
    print("🔄 Reset to Pure MobileFaceNet System")
    print("=" * 50)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get all users
        db = SessionLocal()
        users = db.query(models.User).all()
        
        print(f"Found {len(users)} users in database:")
        for user in users:
            has_encoding = "✅" if user.face_encoding else "❌"
            print(f"  - {user.name} (ID: {user.id}) {has_encoding}")
        
        print("\n⚠️  WARNING: This will clear ALL face encodings!")
        print("All users will need to re-register their faces with the new MobileFaceNet system.")
        print("This ensures 100% compatibility and accuracy.")
        
        confirm = input("\nDo you want to proceed? (type 'YES' to confirm): ")
        if confirm != 'YES':
            print("❌ Operation cancelled.")
            return
        
        # Clear all face encodings
        cleared_count = 0
        for user in users:
            if user.face_encoding:
                user.face_encoding = None
                cleared_count += 1
        
        # Commit changes
        db.commit()
        
        print(f"✅ Successfully cleared {cleared_count} face encodings")
        print("\n🎯 System is now ready for pure MobileFaceNet registration!")
        print("\nNext steps:")
        print("1. Restart the application")
        print("2. Go to 'Register User' page")
        print("3. Register each user with their face")
        print("4. The new system will provide much better accuracy!")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def show_current_status():
    """Show current encoding status"""
    print("📊 Current System Status")
    print("=" * 30)
    
    try:
        from app.core.database import SessionLocal
        from app.models import models
        import numpy as np
        
        db = SessionLocal()
        users = db.query(models.User).all()
        
        mobilefacenet_count = 0
        arcface_count = 0
        other_count = 0
        no_encoding_count = 0
        
        for user in users:
            if user.face_encoding:
                try:
                    # Try to determine encoding type
                    encoding_f32 = np.frombuffer(user.face_encoding, dtype=np.float32)
                    encoding_f64 = np.frombuffer(user.face_encoding, dtype=np.float64)
                    
                    if len(encoding_f32) == 128:
                        # Could be MobileFaceNet
                        mobilefacenet_count += 1
                        print(f"  {user.name}: MobileFaceNet (128D float32)")
                    elif len(encoding_f32) == 512:
                        # Likely ArcFace
                        arcface_count += 1
                        print(f"  {user.name}: ArcFace (512D float32)")
                    elif len(encoding_f64) == 128:
                        # Likely face_recognition
                        other_count += 1
                        print(f"  {user.name}: face_recognition (128D float64)")
                    else:
                        other_count += 1
                        print(f"  {user.name}: Unknown format")
                except:
                    other_count += 1
                    print(f"  {user.name}: Corrupted encoding")
            else:
                no_encoding_count += 1
                print(f"  {user.name}: No encoding")
        
        print(f"\n📈 Summary:")
        print(f"  MobileFaceNet: {mobilefacenet_count}")
        print(f"  ArcFace: {arcface_count}")
        print(f"  Other/Unknown: {other_count}")
        print(f"  No encoding: {no_encoding_count}")
        
        if arcface_count > 0 or other_count > 0:
            print(f"\n⚠️  {arcface_count + other_count} users have incompatible encodings")
            print("Consider running the reset to ensure best accuracy.")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("MobileFaceNet System Reset Tool")
    print("This tool helps transition to pure MobileFaceNet for best accuracy")
    print()
    
    # Show current status
    show_current_status()
    
    print("\nOptions:")
    print("1. Reset all encodings (recommended for best accuracy)")
    print("2. Exit")
    
    choice = input("\nEnter your choice (1-2): ")
    
    if choice == "1":
        reset_to_mobilefacenet()
    else:
        print("Exited without changes.")
