#!/usr/bin/env python3
"""
Test script to verify MediaPipe + MobileFaceNet setup
Run this script to check if all dependencies are properly installed
"""

import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_basic_imports():
    """Test basic Python imports"""
    print("[TEST] Testing basic imports...")

    try:
        import numpy as np
        print("[PASS] NumPy imported successfully")
    except ImportError as e:
        print(f"[FAIL] NumPy import failed: {e}")
        return False

    try:
        import cv2
        print("[PASS] OpenCV imported successfully")
    except ImportError as e:
        print(f"[FAIL] OpenCV import failed: {e}")
        return False

    return True

def test_mediapipe():
    """Test MediaPipe installation"""
    print("\n[TEST] Testing MediaPipe...")

    try:
        import mediapipe as mp
        print("[PASS] MediaPipe imported successfully")

        # Test face detection initialization
        mp_face_detection = mp.solutions.face_detection
        face_detection = mp_face_detection.FaceDetection(
            model_selection=0,
            min_detection_confidence=0.5
        )
        print("[PASS] MediaPipe Face Detection initialized successfully")

        return True

    except ImportError as e:
        print(f"[FAIL] MediaPipe import failed: {e}")
        print("   Install with: pip install mediapipe")
        return False
    except Exception as e:
        print(f"[FAIL] MediaPipe initialization failed: {e}")
        return False

def test_openvino():
    """Test OpenVINO installation"""
    print("\n[TEST] Testing OpenVINO...")

    try:
        from openvino.runtime import Core
        print("[PASS] OpenVINO imported successfully")

        # Test core initialization
        core = Core()
        print("[PASS] OpenVINO Core initialized successfully")

        # List available devices
        devices = core.available_devices
        print(f"[PASS] Available devices: {devices}")

        return True

    except ImportError as e:
        print(f"[FAIL] OpenVINO import failed: {e}")
        print("   Install with: pip install openvino")
        return False
    except Exception as e:
        print(f"[FAIL] OpenVINO initialization failed: {e}")
        return False

def test_onnx():
    """Test ONNX installation"""
    print("\n[TEST] Testing ONNX...")

    try:
        import onnx
        print("[PASS] ONNX imported successfully")

        import onnxruntime
        print("[PASS] ONNX Runtime imported successfully")

        return True

    except ImportError as e:
        print(f"[FAIL] ONNX import failed: {e}")
        print("   Install with: pip install onnx onnxruntime")
        return False

def test_face_recognition_system():
    """Test the face recognition system"""
    print("\n[TEST] Testing Face Recognition System...")

    try:
        # Add the app directory to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

        from app.services.face_utils import face_recognition_system
        print("[PASS] Face recognition system imported successfully")

        # Check which system is being used
        stats = face_recognition_system.get_performance_stats()
        recognition_system = stats.get('recognition_system', 'Unknown')
        print(f"[PASS] Using recognition system: {recognition_system}")

        if 'MediaPipe' in recognition_system:
            print("[SUCCESS] MediaPipe + MobileFaceNet system is active!")
        elif 'InsightFace' in recognition_system:
            print("[WARN] Using ArcFace fallback system")
        else:
            print("[WARN] Using OpenCV fallback system")

        return True

    except Exception as e:
        print(f"[FAIL] Face recognition system test failed: {e}")
        return False

def test_model_download():
    """Test model download/creation"""
    print("\n[TEST] Testing Model Setup...")

    try:
        from pathlib import Path

        model_dir = Path("app/models")
        model_path = model_dir / "mobilefacenet.onnx"

        if model_path.exists():
            print(f"[PASS] MobileFaceNet model found at: {model_path}")
            print(f"   Model size: {model_path.stat().st_size} bytes")
        else:
            print("[WARN] MobileFaceNet model not found")
            print("   It will be downloaded/created on first use")

        return True

    except Exception as e:
        print(f"[FAIL] Model setup test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("MediaPipe + MobileFaceNet Setup Test")
    print("=" * 50)

    tests = [
        ("Basic Imports", test_basic_imports),
        ("MediaPipe", test_mediapipe),
        ("OpenVINO", test_openvino),
        ("ONNX", test_onnx),
        ("Face Recognition System", test_face_recognition_system),
        ("Model Setup", test_model_download),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"[FAIL] {test_name} test crashed: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("Test Summary:")

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("\n[SUCCESS] All tests passed! MediaPipe + MobileFaceNet system is ready!")
        print("\nNext steps:")
        print("1. Start the application: python -m uvicorn app.main:app --reload")
        print("2. Check logs for MediaPipe initialization messages")
        print("3. Test face recognition with your cameras")
    else:
        print(f"\n[WARN] {total - passed} test(s) failed. Please fix the issues above.")
        print("\nThe system will fall back to ArcFace or OpenCV if MediaPipe is not available.")

    return passed == total

if __name__ == "__main__":
    success = main()

    if success:
        print("\n[SUCCESS] Ready to use MediaPipe + MobileFaceNet for smooth face recognition!")
    else:
        print("\n[WARN] Installation had issues, but the system will still work with fallback models")

    sys.exit(0 if success else 1)
