#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to re-register a user with the new MobileFaceNet system
This will capture a new face encoding that's compatible with the current system
"""

import sys
import os
import cv2
import numpy as np
import time

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def reregister_user():
    """Re-register a user with the new face recognition system"""
    print("Face Re-registration Tool")
    print("=" * 40)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Get existing users
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.is_active == True).all()
        
        if len(users) == 0:
            print("No users found in database!")
            return
        
        print("Existing users:")
        for i, user in enumerate(users):
            print(f"  {i+1}. {user.name} (ID: {user.id})")
        
        # Select user to re-register
        while True:
            try:
                choice = input(f"\nSelect user to re-register (1-{len(users)}) or 'q' to quit: ")
                if choice.lower() == 'q':
                    return
                
                user_index = int(choice) - 1
                if 0 <= user_index < len(users):
                    selected_user = users[user_index]
                    break
                else:
                    print("Invalid choice!")
            except ValueError:
                print("Please enter a number!")
        
        print(f"\nRe-registering user: {selected_user.name}")
        print("Please position your face in front of the camera...")
        print("Press SPACE to capture, 'q' to quit")
        
        # Open webcam
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Cannot open webcam!")
            return
        
        captured_encoding = None
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Show frame
            cv2.imshow('Face Re-registration', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' '):  # Space to capture
                print("Capturing face...")
                
                # Process face recognition
                results = face_recognition_system.recognize_faces_in_frame(frame)
                
                if len(results) == 0:
                    print("No face detected! Try again.")
                    continue
                elif len(results) > 1:
                    print("Multiple faces detected! Please ensure only one face is visible.")
                    continue
                
                # Extract face from the frame
                result = results[0]
                top, right, bottom, left = result['location']
                
                # Crop face
                face_crop = frame[top:bottom, left:right]
                
                if face_crop.size == 0:
                    print("Invalid face crop! Try again.")
                    continue
                
                # Extract new encoding using current system
                if hasattr(face_recognition_system, 'mediapipe_system'):
                    encoding = face_recognition_system.mediapipe_system.extract_face_embedding_openvino(face_crop)
                else:
                    print("MediaPipe system not available!")
                    break
                
                if encoding is not None:
                    captured_encoding = encoding
                    print(f"✅ Face encoding captured! Dimensions: {len(encoding)}")
                    break
                else:
                    print("Failed to extract face encoding! Try again.")
        
        cap.release()
        cv2.destroyAllWindows()
        
        if captured_encoding is not None:
            # Update user in database
            try:
                # Convert encoding to bytes
                encoding_bytes = captured_encoding.tobytes()
                
                # Update user
                selected_user.face_encoding = encoding_bytes
                db.commit()
                
                print(f"✅ Successfully updated face encoding for {selected_user.name}")
                print("The user should now be recognized correctly!")
                
                # Reload face recognition system
                users_data = []
                for user in db.query(models.User).filter(models.User.is_active == True).all():
                    users_data.append({
                        'id': user.id,
                        'name': user.name,
                        'face_encoding': user.face_encoding
                    })
                
                face_recognition_system.load_known_faces(users_data)
                print("Face recognition system reloaded with new encoding.")
                
            except Exception as e:
                print(f"Error updating database: {e}")
                db.rollback()
        else:
            print("No face encoding captured.")
        
        db.close()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("This tool will help you re-register with the new MobileFaceNet system.")
    print("This is recommended for better recognition accuracy.")
    print()
    
    confirm = input("Do you want to proceed? (y/n): ")
    if confirm.lower() == 'y':
        reregister_user()
    else:
        print("Cancelled.")
