# type: ignore
from __future__ import annotations
from openvino._pyopenvino import get_version as get_ie_version
import re as re
__all__ = ['SingletonMetaClass', 'VersionChecker', 'extract_hash_from_version', 'extract_release_version', 'get_ie_version', 'get_simplified_ie_version', 're', 'simplify_version']
class SingletonMetaClass(type):
    @classmethod
    def __call__(cls, *args, **kwargs):
        ...
    def __init__(self, cls_name, super_classes, dic):
        ...
class VersionChecker:
    _SingletonMetaClass__single_instance = None
    def __init__(self):
        ...
    def get_ie_simplified_version(self):
        ...
    def get_ie_version(self):
        ...
def extract_hash_from_version(full_version: str):
    ...
def extract_release_version(version: str):
    ...
def get_simplified_ie_version(version = None):
    ...
def simplify_version(version: str):
    ...
