import cv2
import numpy as np
import openvino as ov
import time
import logging
import os
from typing import Optional, Dict, List
from pathlib import Path

logger = logging.getLogger(__name__)

class MobileFaceNetOpenVINO:
    """
    MobileFaceNet face recognition using OpenVINO runtime
    Optimized for CPU inference with high performance
    """
    
    def __init__(self, 
                 model_path: str = "models/mobilefacenet.onnx",
                 input_size: tuple = (112, 112),
                 similarity_threshold: float = 0.5):
        """
        Initialize MobileFaceNet with OpenVINO
        
        Args:
            model_path: Path to MobileFaceNet ONNX model
            input_size: Input image size (width, height)
            similarity_threshold: Cosine similarity threshold for recognition
        """
        self.model_path = model_path
        self.input_size = input_size
        self.similarity_threshold = similarity_threshold
        
        # OpenVINO components
        self.core = None
        self.model = None
        self.compiled_model = None
        self.input_layer = None
        self.output_layer = None
        
        # Performance metrics
        self.inference_times = []
        self.total_inferences = 0
        
        # Initialize OpenVINO
        self._initialize_openvino()
    
    def _initialize_openvino(self):
        """Initialize OpenVINO runtime and load model"""
        try:
            logger.info("🔄 Initializing OpenVINO runtime...")
            
            # Initialize OpenVINO Core
            self.core = ov.Core()
            
            # Check if model file exists
            if not os.path.exists(self.model_path):
                logger.warning(f"⚠️ Model file not found: {self.model_path}")
                logger.info("🔄 Creating dummy MobileFaceNet model for testing...")
                self._create_dummy_model()
                return
            
            # Read the model
            logger.info(f"🔄 Loading model: {self.model_path}")
            self.model = self.core.read_model(self.model_path)
            
            # Compile model for CPU with optimizations
            self.compiled_model = self.core.compile_model(
                self.model, 
                "CPU",
                {
                    "PERFORMANCE_HINT": "LATENCY",
                    "CPU_THREADS_NUM": "0",  # Use all available cores
                    "INFERENCE_PRECISION_HINT": "f32"
                }
            )
            
            # Get input and output layers
            self.input_layer = self.compiled_model.input(0)
            self.output_layer = self.compiled_model.output(0)
            
            logger.info(f"✅ OpenVINO MobileFaceNet initialized successfully")
            logger.info(f"   Input shape: {self.input_layer.shape}")
            logger.info(f"   Output shape: {self.output_layer.shape}")
            
        except Exception as e:
            logger.error(f"❌ OpenVINO initialization failed: {e}")
            logger.info("🔄 Creating dummy model for fallback...")
            self._create_dummy_model()
    
    def _create_dummy_model(self):
        """Create a dummy model for testing when ONNX model is not available"""
        logger.info("🔄 Using dummy MobileFaceNet model (512-dimensional embeddings)")
        self.compiled_model = None  # Mark as dummy mode
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """
        Preprocess face image for MobileFaceNet inference
        
        Args:
            face_image: Input face image (BGR)
            
        Returns:
            Preprocessed image tensor
        """
        try:
            # Resize to model input size
            resized = cv2.resize(face_image, self.input_size)
            
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
            
            # Normalize to [0, 1]
            normalized = rgb_image.astype(np.float32) / 255.0
            
            # Normalize to [-1, 1] (common for face recognition models)
            normalized = (normalized - 0.5) / 0.5
            
            # Add batch dimension and transpose to NCHW format
            input_tensor = np.transpose(normalized, (2, 0, 1))  # HWC to CHW
            input_tensor = np.expand_dims(input_tensor, axis=0)  # Add batch dimension
            
            return input_tensor
            
        except Exception as e:
            logger.error(f"❌ Face preprocessing error: {e}")
            return None
    
    def extract_embedding(self, face_image: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract face embedding using MobileFaceNet
        
        Args:
            face_image: Input face image (BGR)
            
        Returns:
            Normalized face embedding vector or None if failed
        """
        start_time = time.time()
        
        try:
            # Preprocess face image
            input_tensor = self.preprocess_face(face_image)
            if input_tensor is None:
                return None
            
            # Handle dummy model case
            if self.compiled_model is None:
                # Return dummy 512-dimensional embedding
                dummy_embedding = np.random.randn(512).astype(np.float32)
                dummy_embedding = dummy_embedding / np.linalg.norm(dummy_embedding)
                
                inference_time = time.time() - start_time
                self.inference_times.append(inference_time)
                self.total_inferences += 1
                
                logger.debug(f"🔄 Generated dummy embedding in {inference_time:.3f}s")
                return dummy_embedding
            
            # Run inference
            result = self.compiled_model([input_tensor])
            
            # Get embedding from output
            embedding = list(result.values())[0][0]  # Remove batch dimension
            
            # Normalize embedding
            embedding = embedding / np.linalg.norm(embedding)
            
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.total_inferences += 1
            
            logger.debug(f"🔍 MobileFaceNet embedding extracted in {inference_time:.3f}s")
            
            return embedding.astype(np.float32)
            
        except Exception as e:
            logger.error(f"❌ MobileFaceNet embedding extraction error: {e}")
            return None
    
    def compare_embeddings(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Compare two face embeddings using cosine similarity
        
        Args:
            embedding1: First face embedding
            embedding2: Second face embedding
            
        Returns:
            Cosine similarity score (0-1)
        """
        try:
            # Ensure embeddings are normalized
            embedding1 = embedding1 / np.linalg.norm(embedding1)
            embedding2 = embedding2 / np.linalg.norm(embedding2)
            
            # Calculate cosine similarity
            similarity = np.dot(embedding1, embedding2)
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"❌ Embedding comparison error: {e}")
            return 0.0
    
    def is_same_person(self, embedding1: np.ndarray, embedding2: np.ndarray) -> bool:
        """
        Check if two embeddings belong to the same person
        
        Args:
            embedding1: First face embedding
            embedding2: Second face embedding
            
        Returns:
            True if same person, False otherwise
        """
        similarity = self.compare_embeddings(embedding1, embedding2)
        return similarity >= self.similarity_threshold
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        if not self.inference_times:
            return {
                'avg_inference_time': 0.0,
                'min_inference_time': 0.0,
                'max_inference_time': 0.0,
                'total_inferences': 0,
                'fps_estimate': 0.0
            }
        
        avg_time = np.mean(self.inference_times)
        min_time = np.min(self.inference_times)
        max_time = np.max(self.inference_times)
        fps_estimate = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            'avg_inference_time': avg_time,
            'min_inference_time': min_time,
            'max_inference_time': max_time,
            'total_inferences': self.total_inferences,
            'fps_estimate': fps_estimate
        }
    
    def __del__(self):
        """Cleanup resources"""
        try:
            # OpenVINO resources are automatically cleaned up
            pass
        except:
            pass
