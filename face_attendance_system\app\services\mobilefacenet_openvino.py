"""
MobileFaceNet OpenVINO Service
High-performance face recognition using MobileFaceNet with OpenVINO optimization
"""

import cv2
import numpy as np
from typing import Optional, List
import logging
import time
import os
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class MobileFaceNetOpenVINO:
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize MobileFaceNet with OpenVINO
        
        Args:
            model_path: Path to MobileFaceNet ONNX model
        """
        self.model_path = model_path or "models/mobilefacenet.onnx"
        self.input_size = (112, 112)  # MobileFaceNet input size
        self.embedding_size = 512  # MobileFaceNet embedding dimension
        
        # OpenVINO components
        self.core = None
        self.model = None
        self.compiled_model = None
        self.input_layer = None
        self.output_layer = None
        
        # Performance metrics
        self.inference_times = []
        self.total_inferences = 0
        
        # Initialize OpenVINO
        self._initialize_openvino()
    
    def _initialize_openvino(self):
        """Initialize OpenVINO runtime and load model"""
        try:
            logger.info("🔄 Initializing OpenVINO runtime...")
            
            # Import OpenVINO
            import openvino as ov
            
            # Create OpenVINO Core
            self.core = ov.Core()
            
            # Check if model file exists
            if not os.path.exists(self.model_path):
                logger.warning(f"⚠️ Model file not found: {self.model_path}")
                logger.info("🔄 Creating dummy MobileFaceNet model for testing...")
                self._create_dummy_model()
                return
            
            # Load the model
            logger.info(f"🔄 Loading MobileFaceNet model: {self.model_path}")
            self.model = self.core.read_model(self.model_path)
            
            # Compile model for CPU with optimizations
            self.compiled_model = self.core.compile_model(
                self.model,
                "CPU",
                {
                    "PERFORMANCE_HINT": "LATENCY",
                    "CPU_THREADS_NUM": "0",  # Use all available cores
                    "INFERENCE_PRECISION_HINT": "f32"
                }
            )
            
            # Get input and output layers
            self.input_layer = self.compiled_model.input(0)
            self.output_layer = self.compiled_model.output(0)
            
            logger.info(f"✅ MobileFaceNet model loaded successfully")
            logger.info(f"   Input shape: {self.input_layer.shape}")
            logger.info(f"   Output shape: {self.output_layer.shape}")
            
        except Exception as e:
            logger.error(f"❌ OpenVINO initialization failed: {e}")
            logger.info("🔄 Creating dummy MobileFaceNet model for testing...")
            self._create_dummy_model()
    
    def _create_dummy_model(self):
        """Create a dummy model for testing when real model is not available"""
        logger.info("🔄 Using dummy MobileFaceNet model (512-dimensional embeddings)")
        self.compiled_model = None  # Mark as dummy
    
    def preprocess_face(self, face_image: np.ndarray) -> np.ndarray:
        """
        Preprocess face image for MobileFaceNet
        
        Args:
            face_image: Input face image
            
        Returns:
            Preprocessed image tensor
        """
        try:
            # Resize to model input size
            resized = cv2.resize(face_image, self.input_size)
            
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
            
            # Normalize to [-1, 1] (MobileFaceNet standard)
            normalized = (rgb_image.astype(np.float32) - 127.5) / 127.5
            
            # Add batch dimension and transpose to NCHW format
            input_tensor = np.transpose(normalized, (2, 0, 1))  # HWC to CHW
            input_tensor = np.expand_dims(input_tensor, axis=0)  # Add batch dimension
            
            return input_tensor
            
        except Exception as e:
            logger.error(f"❌ Face preprocessing error: {e}")
            return None
    
    def extract_embedding(self, face_image: np.ndarray) -> Optional[np.ndarray]:
        """
        Extract face embedding using MobileFaceNet
        
        Args:
            face_image: Input face image
            
        Returns:
            512-dimensional face embedding or None if failed
        """
        start_time = time.time()
        
        try:
            # Preprocess the face
            input_tensor = self.preprocess_face(face_image)
            if input_tensor is None:
                return None
            
            # Run inference
            if self.compiled_model is None:
                # Dummy model - return random normalized embedding
                embedding = np.random.randn(self.embedding_size).astype(np.float32)
                embedding = embedding / np.linalg.norm(embedding)  # Normalize
                logger.debug("🔄 Using dummy embedding (model not loaded)")
            else:
                # Real model inference
                result = self.compiled_model([input_tensor])
                embedding = result[self.output_layer][0]  # Remove batch dimension
                
                # Normalize embedding
                embedding = embedding / np.linalg.norm(embedding)
            
            # Update metrics
            inference_time = time.time() - start_time
            self.inference_times.append(inference_time)
            self.total_inferences += 1
            
            logger.debug(f"🧠 MobileFaceNet embedding extracted in {inference_time*1000:.2f}ms")
            
            return embedding
            
        except Exception as e:
            logger.error(f"❌ Embedding extraction error: {e}")
            return None
    
    def extract_embeddings_batch(self, face_images: List[np.ndarray]) -> List[Optional[np.ndarray]]:
        """
        Extract embeddings for multiple faces (batch processing)
        
        Args:
            face_images: List of face images
            
        Returns:
            List of embeddings (same order as input)
        """
        embeddings = []
        
        for face_image in face_images:
            embedding = self.extract_embedding(face_image)
            embeddings.append(embedding)
        
        return embeddings
    
    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Compute cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            
        Returns:
            Cosine similarity score (0.0 to 1.0)
        """
        try:
            # Ensure embeddings are normalized
            emb1_norm = embedding1 / np.linalg.norm(embedding1)
            emb2_norm = embedding2 / np.linalg.norm(embedding2)
            
            # Compute cosine similarity
            similarity = np.dot(emb1_norm, emb2_norm)
            
            # Clamp to [0, 1] range
            similarity = np.clip(similarity, 0.0, 1.0)
            
            return float(similarity)
            
        except Exception as e:
            logger.error(f"❌ Similarity computation error: {e}")
            return 0.0
    
    def find_best_match(self, query_embedding: np.ndarray, 
                       known_embeddings: List[np.ndarray],
                       threshold: float = 0.5) -> tuple:
        """
        Find best matching embedding from known embeddings
        
        Args:
            query_embedding: Query face embedding
            known_embeddings: List of known face embeddings
            threshold: Similarity threshold for matching
            
        Returns:
            Tuple of (best_match_index, similarity_score) or (-1, 0.0) if no match
        """
        if not known_embeddings:
            return -1, 0.0
        
        best_similarity = 0.0
        best_index = -1
        
        for i, known_embedding in enumerate(known_embeddings):
            if known_embedding is None:
                continue
                
            similarity = self.compute_similarity(query_embedding, known_embedding)
            
            if similarity > best_similarity and similarity >= threshold:
                best_similarity = similarity
                best_index = i
        
        return best_index, best_similarity
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        if not self.inference_times:
            return {
                'avg_inference_time': 0.0,
                'total_inferences': 0,
                'fps': 0.0
            }
        
        avg_time = np.mean(self.inference_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            'avg_inference_time': avg_time * 1000,  # Convert to ms
            'total_inferences': self.total_inferences,
            'fps': fps
        }
    
    def __del__(self):
        """Cleanup resources"""
        try:
            # OpenVINO resources are automatically cleaned up
            pass
        except:
            pass
