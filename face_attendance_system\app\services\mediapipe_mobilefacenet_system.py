import cv2
import numpy as np
import time
import logging
from typing import List, Dict, Optional
from sqlalchemy.orm import Session

from .mediapipe_face_detection import MediaPipeFaceDetector
from .mobilefacenet_openvino import MobileFaceNetOpenVINO

logger = logging.getLogger(__name__)

class MediaPipeMobileFaceNetSystem:
    """
    Unified face recognition system using MediaPipe Face Detection + MobileFaceNet (OpenVINO)
    Designed to replace InsightFace ArcFace while maintaining the same API interface
    """
    
    def __init__(self, 
                 similarity_threshold: float = 0.5,
                 detection_confidence: float = 0.5,
                 model_path: str = "models/mobilefacenet.onnx"):
        """
        Initialize MediaPipe + MobileFaceNet face recognition system
        
        Args:
            similarity_threshold: Cosine similarity threshold for face recognition
            detection_confidence: Minimum confidence for face detection
            model_path: Path to MobileFaceNet ONNX model
        """
        self.similarity_threshold = similarity_threshold
        self.detection_confidence = detection_confidence
        self.model_path = model_path
        
        # Known faces database
        self.known_encodings = []
        self.known_names = []
        self.known_user_ids = []
        
        # Performance metrics
        self.detection_times = []
        self.recognition_times = []
        self.total_detections = 0
        self.successful_recognitions = 0
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize MediaPipe detector and MobileFaceNet recognizer"""
        try:
            logger.info("🔄 Initializing MediaPipe + MobileFaceNet system...")
            
            # Initialize MediaPipe Face Detection
            self.face_detector = MediaPipeFaceDetector(
                model_selection=0,  # Short-range model for speed
                min_detection_confidence=self.detection_confidence
            )
            
            # Initialize MobileFaceNet with OpenVINO
            self.face_recognizer = MobileFaceNetOpenVINO(
                model_path=self.model_path,
                similarity_threshold=self.similarity_threshold
            )
            
            logger.info("✅ MediaPipe + MobileFaceNet system initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize MediaPipe + MobileFaceNet system: {e}")
            raise Exception(f"System initialization failed: {e}")
    
    def extract_face_encoding(self, image_path: str) -> Optional[bytes]:
        """
        Extract face encoding from image file (for user registration)
        
        Args:
            image_path: Path to image file
            
        Returns:
            Face encoding as bytes or None if failed
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Could not load image: {image_path}")
                return None
            
            # Detect faces
            faces = self.face_detector.detect_faces(image)
            
            if not faces:
                logger.warning(f"No faces detected in {image_path}")
                return None
            
            if len(faces) > 1:
                logger.warning(f"Multiple faces detected in {image_path}, using the first one")
            
            # Use the first (largest) face
            face = faces[0]
            
            # Crop face
            face_crop = self.face_detector.crop_face(image, face['bbox'])
            if face_crop is None:
                logger.error(f"Failed to crop face from {image_path}")
                return None
            
            # Extract embedding
            embedding = self.face_recognizer.extract_embedding(face_crop)
            if embedding is None:
                logger.error(f"Failed to extract embedding from {image_path}")
                return None
            
            logger.info(f"✅ Face encoding extracted from {image_path}")
            
            # Convert to bytes for database storage
            return embedding.astype(np.float32).tobytes()
            
        except Exception as e:
            logger.error(f"❌ Error extracting face encoding from {image_path}: {e}")
            return None
    
    def load_known_faces(self, db: Session):
        """
        Load known faces from database
        
        Args:
            db: Database session
        """
        try:
            from ..models.models import User
            
            # Clear existing data
            self.known_encodings.clear()
            self.known_names.clear()
            self.known_user_ids.clear()
            
            # Load users from database
            users = db.query(User).all()
            
            for user in users:
                try:
                    if user.face_encoding:
                        # Load encoding from bytes
                        encoding = np.frombuffer(user.face_encoding, dtype=np.float32)
                        
                        # Normalize the embedding
                        encoding = encoding / np.linalg.norm(encoding)
                        
                        self.known_encodings.append(encoding)
                        self.known_names.append(user.name)
                        self.known_user_ids.append(user.id)
                        
                except Exception as e:
                    logger.error(f"Error loading encoding for user {user.name}: {e}")
            
            logger.info(f"✅ Loaded {len(self.known_encodings)} known face embeddings")
            if len(self.known_encodings) > 0:
                logger.info(f"   Registered users: {', '.join(self.known_names)}")
                
        except Exception as e:
            logger.error(f"❌ Error loading known faces: {e}")
    
    def recognize_faces_in_frame(self, frame) -> List[Dict]:
        """
        Recognize faces in a frame
        
        Args:
            frame: Input BGR image frame
            
        Returns:
            List of dictionaries with face recognition results
        """
        if len(self.known_encodings) == 0:
            return []
        
        results = []
        
        try:
            start_time = time.time()
            
            # Detect faces using MediaPipe
            faces = self.face_detector.detect_faces(frame)
            
            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            self.total_detections += len(faces)
            
            if len(faces) > 0:
                logger.debug(f"🔍 MediaPipe detected {len(faces)} face(s) in frame")
            
            for face in faces:
                recognition_start = time.time()
                
                # Crop face
                face_crop = self.face_detector.crop_face(frame, face['bbox'])
                if face_crop is None:
                    continue
                
                # Extract embedding
                embedding = self.face_recognizer.extract_embedding(face_crop)
                if embedding is None:
                    continue
                
                # Initialize recognition results
                name = "Unknown"
                user_id = None
                confidence = 0.0
                
                # Compare with known faces
                if len(self.known_encodings) > 0:
                    similarities = []
                    for known_embedding in self.known_encodings:
                        similarity = self.face_recognizer.compare_embeddings(embedding, known_embedding)
                        similarities.append(similarity)
                    
                    similarities = np.array(similarities)
                    best_match_index = np.argmax(similarities)
                    best_similarity = similarities[best_match_index]
                    
                    if best_similarity >= self.similarity_threshold:
                        name = self.known_names[best_match_index]
                        user_id = self.known_user_ids[best_match_index]
                        confidence = best_similarity
                        self.successful_recognitions += 1
                        logger.info(f"👤 FACE RECOGNIZED: {name} (ID: {user_id}, similarity: {confidence:.3f})")
                
                recognition_time = time.time() - recognition_start
                self.recognition_times.append(recognition_time)
                
                # Convert bbox format to match existing system
                bbox = face['bbox']
                x, y, width, height = bbox
                location = (y, x + width, y + height, x)  # (top, right, bottom, left)
                
                results.append({
                    'name': name,
                    'user_id': user_id,
                    'confidence': confidence,
                    'location': location,
                    'is_known': user_id is not None,
                    'detection_time': detection_time,
                    'recognition_time': recognition_time
                })
            
        except Exception as e:
            logger.error(f"❌ Error in face recognition: {e}")
        
        return results
    
    def draw_face_boxes(self, frame, recognition_results: List[Dict]) -> np.ndarray:
        """
        Draw face recognition results on frame
        
        Args:
            frame: Input BGR image frame
            recognition_results: List of recognition results
            
        Returns:
            Frame with drawn face boxes and labels
        """
        try:
            annotated_frame = frame.copy()
            
            for result in recognition_results:
                name = result['name']
                confidence = result['confidence']
                location = result['location']
                is_known = result['is_known']
                
                # Convert location format
                top, right, bottom, left = location
                
                # Choose color based on recognition status
                color = (0, 255, 0) if is_known else (0, 0, 255)  # Green for known, Red for unknown
                
                # Draw bounding box
                cv2.rectangle(annotated_frame, (left, top), (right, bottom), color, 2)
                
                # Draw label
                if is_known:
                    label = f"{name} ({confidence:.2f})"
                else:
                    label = "Unknown"
                
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(annotated_frame, (left, top - label_size[1] - 10), 
                             (left + label_size[0], top), color, -1)
                cv2.putText(annotated_frame, label, (left, top - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            return annotated_frame
            
        except Exception as e:
            logger.error(f"❌ Error drawing face boxes: {e}")
            return frame
    
    def get_performance_stats(self) -> Dict:
        """Get comprehensive performance statistics"""
        detector_stats = self.face_detector.get_performance_stats()
        recognizer_stats = self.face_recognizer.get_performance_stats()
        
        return {
            'detection': detector_stats,
            'recognition': recognizer_stats,
            'total_detections': self.total_detections,
            'successful_recognitions': self.successful_recognitions,
            'recognition_rate': self.successful_recognitions / max(1, self.total_detections)
        }
