"""
Integrated MediaPipe + MobileFaceNet Face Recognition System
Combines MediaPipe face detection with MobileFaceNet recognition via OpenVINO
"""

import cv2
import numpy as np
from typing import List, Dict, Optional, Tuple
import logging
import time

from .mediapipe_face_detection import MediaPipeFaceDetection
from .mobilefacenet_openvino import MobileFaceNetOpenVINO

# Configure logging
logger = logging.getLogger(__name__)

class MediaPipeMobileFaceNetSystem:
    def __init__(self, 
                 detection_confidence: float = 0.5,
                 recognition_threshold: float = 0.5,
                 model_path: Optional[str] = None):
        """
        Initialize integrated MediaPipe + MobileFaceNet system
        
        Args:
            detection_confidence: Face detection confidence threshold
            recognition_threshold: Face recognition similarity threshold
            model_path: Path to MobileFaceNet ONNX model
        """
        self.detection_confidence = detection_confidence
        self.recognition_threshold = recognition_threshold
        
        # Known faces database
        self.known_embeddings = []
        self.known_names = []
        self.known_user_ids = []
        
        # Performance metrics
        self.total_frames_processed = 0
        self.total_faces_detected = 0
        self.total_faces_recognized = 0
        
        # Initialize components
        logger.info("🔄 Initializing MediaPipe + MobileFaceNet system...")
        
        # Initialize MediaPipe face detection
        self.face_detector = MediaPipeFaceDetection(
            min_detection_confidence=detection_confidence,
            model_selection=0  # Short-range model for better performance
        )
        
        # Initialize MobileFaceNet recognition
        self.face_recognizer = MobileFaceNetOpenVINO(model_path=model_path)
        
        logger.info("✅ MediaPipe + MobileFaceNet system initialized successfully")
    
    def load_known_faces(self, users_data: List[Dict]) -> int:
        """
        Load known faces from user data
        
        Args:
            users_data: List of user dictionaries with face encodings
            
        Returns:
            Number of faces loaded successfully
        """
        self.known_embeddings.clear()
        self.known_names.clear()
        self.known_user_ids.clear()
        
        loaded_count = 0
        
        for user in users_data:
            if user.get('face_encoding'):
                try:
                    # Load face encoding
                    encoding = np.frombuffer(user['face_encoding'], dtype=np.float32)
                    
                    # Validate embedding dimension (should be 512 for MobileFaceNet)
                    if len(encoding) == 512:
                        # Normalize the embedding
                        encoding = encoding / np.linalg.norm(encoding)
                        
                        self.known_embeddings.append(encoding)
                        self.known_names.append(user['name'])
                        self.known_user_ids.append(user['id'])
                        loaded_count += 1
                        
                        logger.debug(f"Loaded MobileFaceNet embedding for {user['name']}")
                    else:
                        logger.warning(f"Invalid embedding dimension {len(encoding)} for {user['name']} (expected 512)")
                        
                except Exception as e:
                    logger.error(f"Failed to load encoding for {user['name']}: {e}")
        
        logger.info(f"✅ Loaded {loaded_count} known faces for recognition")
        return loaded_count
    
    def process_frame(self, frame: np.ndarray) -> Tuple[np.ndarray, List[Dict]]:
        """
        Process a single frame for face detection and recognition
        
        Args:
            frame: Input video frame
            
        Returns:
            Tuple of (annotated_frame, recognition_results)
        """
        start_time = time.time()
        
        try:
            # Step 1: Detect faces using MediaPipe
            detected_faces = self.face_detector.detect_faces(frame)
            
            recognition_results = []
            annotated_frame = frame.copy()
            
            if detected_faces:
                self.total_faces_detected += len(detected_faces)
                
                # Step 2: Process each detected face
                for face in detected_faces:
                    bbox = face['bbox']
                    confidence = face['confidence']
                    
                    # Extract face region
                    face_region = self.face_detector.extract_face_region(frame, bbox)
                    
                    if face_region is not None and face_region.size > 0:
                        # Step 3: Extract embedding using MobileFaceNet
                        embedding = self.face_recognizer.extract_embedding(face_region)
                        
                        if embedding is not None:
                            # Step 4: Find best match
                            best_match_idx, similarity = self.face_recognizer.find_best_match(
                                embedding, self.known_embeddings, self.recognition_threshold
                            )
                            
                            # Prepare result
                            result = {
                                'bbox': bbox,
                                'detection_confidence': confidence,
                                'similarity': similarity,
                                'name': 'Unknown',
                                'user_id': None,
                                'recognized': False
                            }
                            
                            # Check if we have a match
                            if best_match_idx >= 0:
                                result['name'] = self.known_names[best_match_idx]
                                result['user_id'] = self.known_user_ids[best_match_idx]
                                result['recognized'] = True
                                self.total_faces_recognized += 1
                                
                                logger.debug(f"👤 Recognized: {result['name']} (similarity: {similarity:.3f})")
                            else:
                                logger.debug(f"❓ Unknown person (best similarity: {similarity:.3f})")
                            
                            recognition_results.append(result)
                            
                            # Draw on frame
                            self._draw_recognition_result(annotated_frame, result)
            
            # Update metrics
            self.total_frames_processed += 1
            processing_time = time.time() - start_time
            
            logger.debug(f"🎬 Frame processed in {processing_time*1000:.2f}ms "
                        f"({len(detected_faces)} faces detected, {len(recognition_results)} processed)")
            
            return annotated_frame, recognition_results
            
        except Exception as e:
            logger.error(f"❌ Frame processing error: {e}")
            return frame, []
    
    def _draw_recognition_result(self, frame: np.ndarray, result: Dict):
        """Draw recognition result on frame"""
        try:
            x, y, w, h = result['bbox']
            
            # Choose color based on recognition status
            if result['recognized']:
                color = (0, 255, 0)  # Green for recognized
                label = f"{result['name']} ({result['similarity']:.2f})"
            else:
                color = (0, 0, 255)  # Red for unknown
                label = f"Unknown ({result['similarity']:.2f})"
            
            # Draw bounding box
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            # Draw label background
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(frame, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            
            # Draw label text
            cv2.putText(frame, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
        except Exception as e:
            logger.error(f"❌ Drawing error: {e}")
    
    def register_face(self, face_image: np.ndarray, name: str, user_id: int) -> Optional[np.ndarray]:
        """
        Register a new face for recognition
        
        Args:
            face_image: Face image to register
            name: Person's name
            user_id: User ID
            
        Returns:
            Face embedding if successful, None otherwise
        """
        try:
            # Extract embedding
            embedding = self.face_recognizer.extract_embedding(face_image)
            
            if embedding is not None:
                # Add to known faces
                self.known_embeddings.append(embedding)
                self.known_names.append(name)
                self.known_user_ids.append(user_id)
                
                logger.info(f"✅ Registered face for {name} (ID: {user_id})")
                return embedding
            else:
                logger.error(f"❌ Failed to extract embedding for {name}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Face registration error for {name}: {e}")
            return None
    
    def get_performance_stats(self) -> Dict:
        """Get comprehensive performance statistics"""
        detection_stats = self.face_detector.get_performance_stats()
        recognition_stats = self.face_recognizer.get_performance_stats()
        
        return {
            'frames_processed': self.total_frames_processed,
            'faces_detected': self.total_faces_detected,
            'faces_recognized': self.total_faces_recognized,
            'known_faces_count': len(self.known_embeddings),
            'detection': detection_stats,
            'recognition': recognition_stats,
            'recognition_rate': (self.total_faces_recognized / max(1, self.total_faces_detected)) * 100
        }
    
    def __del__(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'face_detector'):
                del self.face_detector
            if hasattr(self, 'face_recognizer'):
                del self.face_recognizer
        except:
            pass
