#!/usr/bin/env python3
"""
Performance optimization script for MediaPipe + MobileFaceNet system
"""

import os
import sys
import logging

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def optimize_mediapipe_settings():
    """Optimize MediaPipe face detection settings"""
    logger.info("🔧 Optimizing MediaPipe settings...")
    
    # Set environment variables for optimal performance
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'  # Enable oneDNN optimizations
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'   # Reduce TensorFlow logging
    os.environ['NUMEXPR_MAX_THREADS'] = '8'    # Optimize NumExpr threading
    
    # MediaPipe specific optimizations
    os.environ['MEDIAPIPE_DISABLE_GPU'] = '1'  # Force CPU usage for consistency
    
    logger.info("✅ MediaPipe environment optimized")

def optimize_openvino_settings():
    """Optimize OpenVINO runtime settings"""
    logger.info("🔧 Optimizing OpenVINO settings...")
    
    # OpenVINO CPU optimizations
    os.environ['OMP_NUM_THREADS'] = '8'        # OpenMP threading
    os.environ['MKL_NUM_THREADS'] = '8'        # Intel MKL threading
    os.environ['OPENVINO_LOG_LEVEL'] = '2'     # Reduce OpenVINO logging
    
    # CPU-specific optimizations
    os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
    os.environ['KMP_BLOCKTIME'] = '1'
    
    logger.info("✅ OpenVINO environment optimized")

def optimize_opencv_settings():
    """Optimize OpenCV settings"""
    logger.info("🔧 Optimizing OpenCV settings...")
    
    # OpenCV threading
    os.environ['OPENCV_NUM_THREADS'] = '8'
    
    # Disable OpenCV's own optimizations that might conflict
    os.environ['OPENCV_DISABLE_AVX2'] = '0'    # Keep AVX2 enabled
    
    logger.info("✅ OpenCV environment optimized")

def create_optimized_config():
    """Create optimized configuration file"""
    logger.info("🔧 Creating optimized configuration...")
    
    config = {
        'mediapipe': {
            'model_selection': 0,  # Short-range model for speed
            'min_detection_confidence': 0.5,
            'max_num_faces': 10,  # Limit max faces for performance
        },
        'mobilefacenet': {
            'input_size': (112, 112),
            'similarity_threshold': 0.5,
            'batch_size': 1,  # Single image processing
        },
        'openvino': {
            'device': 'CPU',
            'performance_hint': 'LATENCY',
            'cpu_threads_num': 0,  # Use all available cores
            'inference_precision_hint': 'f32',
        },
        'processing': {
            'frame_skip': 1,  # Process every frame
            'max_fps': 30,    # Target FPS
            'buffer_size': 3, # Small buffer for low latency
        }
    }
    
    import json
    with open('optimized_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info("✅ Optimized configuration saved to optimized_config.json")
    return config

def benchmark_system():
    """Benchmark the optimized system"""
    logger.info("📊 Running system benchmark...")
    
    try:
        from app.services.mediapipe_mobilefacenet_system import MediaPipeMobileFaceNetSystem
        import cv2
        import numpy as np
        import time
        
        # Initialize system
        system = MediaPipeMobileFaceNetSystem()
        
        # Create test frames
        test_frames = []
        for i in range(10):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            # Add some face-like patterns
            cv2.circle(frame, (320 + i*10, 240), 50, (255, 255, 255), -1)
            cv2.circle(frame, (300 + i*10, 220), 8, (0, 0, 0), -1)
            cv2.circle(frame, (340 + i*10, 220), 8, (0, 0, 0), -1)
            test_frames.append(frame)
        
        # Benchmark detection + recognition
        start_time = time.time()
        total_faces = 0
        
        for frame in test_frames:
            results = system.recognize_faces_in_frame(frame)
            total_faces += len(results)
        
        end_time = time.time()
        total_time = end_time - start_time
        fps = len(test_frames) / total_time
        
        logger.info(f"📊 Benchmark Results:")
        logger.info(f"   Processed {len(test_frames)} frames in {total_time:.3f}s")
        logger.info(f"   Average FPS: {fps:.1f}")
        logger.info(f"   Total faces detected: {total_faces}")
        
        # Get detailed performance stats
        stats = system.get_performance_stats()
        logger.info(f"   Detection stats: {stats['detection']}")
        logger.info(f"   Recognition stats: {stats['recognition']}")
        
        return fps
        
    except Exception as e:
        logger.error(f"❌ Benchmark failed: {e}")
        return 0

def apply_system_optimizations():
    """Apply all system optimizations"""
    logger.info("🚀 Applying system-wide optimizations...")
    
    # Apply environment optimizations
    optimize_mediapipe_settings()
    optimize_openvino_settings()
    optimize_opencv_settings()
    
    # Create optimized config
    config = create_optimized_config()
    
    # Run benchmark
    fps = benchmark_system()
    
    logger.info("✅ All optimizations applied successfully")
    
    if fps > 0:
        logger.info(f"🎯 System performance: {fps:.1f} FPS")
        if fps >= 25:
            logger.info("🎉 Excellent performance! System ready for production.")
        elif fps >= 15:
            logger.info("✅ Good performance! System suitable for most use cases.")
        else:
            logger.warning("⚠️ Performance may need further optimization.")
    
    return config

def main():
    """Main optimization function"""
    logger.info("🔧 MediaPipe + MobileFaceNet Performance Optimization")
    logger.info("=" * 60)
    
    try:
        config = apply_system_optimizations()
        
        logger.info("\n📋 Optimization Summary:")
        logger.info("✅ Environment variables optimized")
        logger.info("✅ Configuration file created")
        logger.info("✅ System benchmark completed")
        
        logger.info("\n🎯 Next Steps:")
        logger.info("1. Restart the application to apply optimizations")
        logger.info("2. Test with real camera feeds")
        logger.info("3. Monitor performance in production")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Optimization failed: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
