#!/usr/bin/env python3
"""
Test script for the complete camera system with MediaPipe + MobileFaceNet
"""

import sys
import os
import cv2
import numpy as np
import logging
import time

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_face_detection_with_real_camera():
    """Test face detection with real camera"""
    try:
        from app.services.mediapipe_face_detection import MediaPipeFaceDetector
        
        logger.info("🔄 Testing MediaPipe face detection with real camera...")
        
        # Initialize detector
        detector = MediaPipeFaceDetector(min_detection_confidence=0.3)
        
        # Open camera
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            logger.error("❌ Cannot open camera 0")
            return False
        
        logger.info("✅ Camera opened. Testing face detection for 10 seconds...")
        logger.info("   Look at the camera to test detection!")
        
        start_time = time.time()
        frame_count = 0
        detection_count = 0
        
        while time.time() - start_time < 10:  # Test for 10 seconds
            ret, frame = cap.read()
            if not ret:
                continue
                
            frame_count += 1
            
            # Detect faces
            faces = detector.detect_faces(frame)
            
            if faces:
                detection_count += 1
                logger.info(f"🔍 Frame {frame_count}: Detected {len(faces)} face(s)")
                for i, face in enumerate(faces):
                    logger.info(f"   Face {i+1}: confidence={face['confidence']:.3f}, bbox={face['bbox']}")
            
            # Show frame with detections (optional)
            if faces:
                annotated_frame = detector.draw_detections(frame, faces)
                cv2.imshow('Face Detection Test', annotated_frame)
            else:
                cv2.imshow('Face Detection Test', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        logger.info(f"✅ Detection test completed:")
        logger.info(f"   Frames processed: {frame_count}")
        logger.info(f"   Frames with detections: {detection_count}")
        logger.info(f"   Detection rate: {detection_count/max(1, frame_count)*100:.1f}%")
        
        return detection_count > 0
        
    except Exception as e:
        logger.error(f"❌ Face detection test failed: {e}")
        return False

def test_face_recognition_system():
    """Test the complete face recognition system"""
    try:
        from app.services.face_utils import FaceRecognitionSystem
        
        logger.info("🔄 Testing complete face recognition system...")
        
        # Initialize system
        system = FaceRecognitionSystem(model_name="mediapipe_mobilefacenet")
        
        # Test with camera
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            logger.error("❌ Cannot open camera 0")
            return False
        
        logger.info("✅ Testing face recognition for 10 seconds...")
        logger.info("   Look at the camera to test recognition!")
        
        start_time = time.time()
        frame_count = 0
        recognition_count = 0
        
        while time.time() - start_time < 10:  # Test for 10 seconds
            ret, frame = cap.read()
            if not ret:
                continue
                
            frame_count += 1
            
            # Recognize faces
            results = system.recognize_faces_in_frame(frame)
            
            if results:
                recognition_count += 1
                logger.info(f"🔍 Frame {frame_count}: Recognized {len(results)} face(s)")
                for i, result in enumerate(results):
                    logger.info(f"   Face {i+1}: {result['name']} (confidence: {result['confidence']:.3f}, known: {result['is_known']})")
                
                # Draw results
                annotated_frame = system.draw_face_boxes(frame, results)
                cv2.imshow('Face Recognition Test', annotated_frame)
            else:
                cv2.imshow('Face Recognition Test', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        
        logger.info(f"✅ Recognition test completed:")
        logger.info(f"   Frames processed: {frame_count}")
        logger.info(f"   Frames with recognition: {recognition_count}")
        logger.info(f"   Recognition rate: {recognition_count/max(1, frame_count)*100:.1f}%")
        
        return recognition_count > 0
        
    except Exception as e:
        logger.error(f"❌ Face recognition test failed: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def test_camera_manager():
    """Test the camera manager system"""
    try:
        from app.services.camera_manager import CameraManager
        from app.core.database import SessionLocal
        
        logger.info("🔄 Testing camera manager...")
        
        # Initialize camera manager
        camera_manager = CameraManager()
        
        # Test with webcam
        db = SessionLocal()
        try:
            logger.info("✅ Starting webcam test...")
            success = camera_manager.start_camera(
                camera_id=999,  # Test ID
                camera_name="Test Webcam",
                rtsp_url="0",  # Webcam
                camera_type="IN",
                db=db
            )
            
            if success:
                logger.info("✅ Camera started successfully!")
                
                # Let it run for 5 seconds
                time.sleep(5)
                
                # Check if camera is active
                active_cameras = camera_manager.get_active_cameras()
                logger.info(f"✅ Active cameras: {len(active_cameras)}")
                
                # Stop camera
                camera_manager.stop_camera(999)
                logger.info("✅ Camera stopped successfully!")
                
                return True
            else:
                logger.error("❌ Failed to start camera")
                return False
                
        finally:
            db.close()
        
    except Exception as e:
        logger.error(f"❌ Camera manager test failed: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all camera system tests"""
    logger.info("🚀 Starting Complete Camera System Tests")
    logger.info("=" * 60)
    
    tests = [
        ("MediaPipe Face Detection", test_face_detection_with_real_camera),
        ("Face Recognition System", test_face_recognition_system),
        ("Camera Manager", test_camera_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        logger.info("-" * 40)
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} PASSED")
        else:
            logger.error(f"❌ {test_name} FAILED")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Camera system is working correctly.")
        logger.info("\n🎯 Next Steps:")
        logger.info("1. Start the web application: python -m uvicorn app.main:app --reload")
        logger.info("2. Go to http://localhost:8000/manage-camera")
        logger.info("3. Add cameras and test face recognition")
        logger.info("4. Register users and test attendance logging")
        return True
    else:
        logger.error(f"⚠️ {total - passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
