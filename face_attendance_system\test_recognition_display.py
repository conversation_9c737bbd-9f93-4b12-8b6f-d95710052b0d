#!/usr/bin/env python3
"""
Test script to diagnose recognition display issues
This script tests if recognition is working but display is failing
"""

import sys
import os
import cv2
import time

# Add app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_recognition_display():
    """Test face recognition and display in real-time"""
    print("Testing Face Recognition Display")
    print("=" * 50)
    
    try:
        from app.services.face_utils import face_recognition_system
        from app.core.database import SessionLocal
        from app.models import models
        
        # Load registered users
        db = SessionLocal()
        users = db.query(models.User).filter(models.User.is_active == True).all()
        
        if len(users) == 0:
            print("No registered users found!")
            return
        
        print(f"Found {len(users)} registered users:")
        for user in users:
            print(f"  - {user.name} (ID: {user.id})")
        
        # Load known faces
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'name': user.name,
                'face_encoding': user.face_encoding
            })
        
        face_recognition_system.load_known_faces(users_data)
        
        # Get system info
        stats = face_recognition_system.get_performance_stats()
        print(f"\nSystem: {stats['recognition_system']}")
        print(f"Loaded faces: {stats['registered_faces']}")
        print(f"Similarity threshold: {stats['similarity_threshold']}")
        
        # Test with webcam
        print("\nStarting webcam test...")
        print("Press 'q' to quit, 's' to save frame")
        
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Cannot open webcam")
            return
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Run recognition
            start_time = time.time()
            results = face_recognition_system.recognize_faces_in_frame(frame)
            recognition_time = time.time() - start_time
            
            # Create display frame
            display_frame = frame.copy()
            
            # Draw results manually to test
            for i, result in enumerate(results):
                top, right, bottom, left = result['location']
                name = result['name']
                confidence = result['confidence']
                is_known = result['is_known']
                
                # Choose color
                color = (0, 255, 0) if is_known else (0, 0, 255)
                
                # Draw rectangle
                cv2.rectangle(display_frame, (left, top), (right, bottom), color, 2)
                
                # Create label
                if is_known:
                    label = f"{name} ({confidence:.2f})"
                else:
                    label = "Unknown"
                
                # Draw label background
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(display_frame, (left, top - 25), (left + label_size[0], top), color, -1)
                
                # Draw label text
                cv2.putText(display_frame, label, (left, top - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                # Print to console
                print(f"Frame {frame_count}: Face {i+1} - Name: '{name}', Known: {is_known}, Confidence: {confidence:.3f}")
            
            # Add frame info
            info_text = f"Frame: {frame_count}, Time: {recognition_time*1000:.1f}ms, Faces: {len(results)}"
            cv2.putText(display_frame, info_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # Show frame
            cv2.imshow('Recognition Test', display_frame)
            
            # Handle keys
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                filename = f"test_frame_{frame_count}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"Saved: {filename}")
        
        cap.release()
        cv2.destroyAllWindows()
        db.close()
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

def test_system_status():
    """Test the current system status"""
    print("\nSystem Status Check")
    print("=" * 30)
    
    try:
        from app.services.face_utils import face_recognition_system
        
        # Check which system is active
        stats = face_recognition_system.get_performance_stats()
        print(f"Recognition System: {stats['recognition_system']}")
        print(f"Similarity Threshold: {stats['similarity_threshold']}")
        print(f"Registered Faces: {stats['registered_faces']}")
        
        # Check MediaPipe system specifically
        if hasattr(face_recognition_system, 'use_mediapipe') and face_recognition_system.use_mediapipe:
            print("MediaPipe System: ACTIVE")
            if hasattr(face_recognition_system, 'mediapipe_system'):
                mp_stats = face_recognition_system.mediapipe_system.get_performance_stats()
                print(f"MediaPipe Threshold: {mp_stats['similarity_threshold']}")
        else:
            print("MediaPipe System: NOT ACTIVE")
        
    except Exception as e:
        print(f"Status check failed: {e}")

if __name__ == "__main__":
    test_system_status()
    
    print("\nStarting recognition display test...")
    print("This will open a window showing live recognition results")
    input("Press Enter to continue...")
    
    test_recognition_display()
