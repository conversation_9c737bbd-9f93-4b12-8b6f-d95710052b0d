#!/usr/bin/env python3
"""
Installation script for MediaPipe + MobileFaceNet dependencies
This script helps install the required packages for the new face recognition system
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    
    if version.major == 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   MediaPipe requires Python 3.7 or higher")
        return False

def install_dependencies():
    """Install the required dependencies"""
    print("\n📦 Installing MediaPipe + MobileFaceNet dependencies...")
    
    # List of packages to install
    packages = [
        ("mediapipe", "MediaPipe for fast face detection"),
        ("openvino", "OpenVINO runtime for CPU optimization"),
        ("onnx", "ONNX model format support"),
        ("onnxruntime", "ONNX runtime for model inference"),
    ]
    
    success_count = 0
    
    for package, description in packages:
        command = f"pip install {package}"
        if run_command(command, f"Installing {package} ({description})"):
            success_count += 1
    
    return success_count == len(packages)

def upgrade_existing_packages():
    """Upgrade existing packages that might conflict"""
    print("\n🔄 Upgrading existing packages...")
    
    packages_to_upgrade = [
        "numpy",
        "opencv-python",
        "pillow"
    ]
    
    for package in packages_to_upgrade:
        command = f"pip install --upgrade {package}"
        run_command(command, f"Upgrading {package}")

def install_from_requirements():
    """Install all requirements from requirements.txt"""
    print("\n📋 Installing from requirements.txt...")
    
    if os.path.exists("requirements.txt"):
        command = "pip install -r requirements.txt"
        return run_command(command, "Installing all requirements")
    else:
        print("⚠️ requirements.txt not found, skipping...")
        return True

def test_installation():
    """Test if the installation was successful"""
    print("\n🧪 Testing installation...")
    
    test_script = "test_mediapipe_setup.py"
    if os.path.exists(test_script):
        command = f"python {test_script}"
        return run_command(command, "Running installation test")
    else:
        print("⚠️ Test script not found, running basic import test...")
        
        # Basic import test
        try:
            import mediapipe
            import openvino
            import onnx
            import onnxruntime
            print("✅ All packages imported successfully")
            return True
        except ImportError as e:
            print(f"❌ Import test failed: {e}")
            return False

def main():
    """Main installation process"""
    print("🚀 MediaPipe + MobileFaceNet Installation Script")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Installation aborted due to incompatible Python version")
        return False
    
    # Upgrade pip first
    print("\n🔄 Upgrading pip...")
    run_command("python -m pip install --upgrade pip", "Upgrading pip")
    
    # Install/upgrade existing packages
    upgrade_existing_packages()
    
    # Install new dependencies
    if not install_dependencies():
        print("\n❌ Failed to install some dependencies")
        print("You can try installing manually:")
        print("   pip install mediapipe openvino onnx onnxruntime")
        return False
    
    # Install from requirements.txt
    install_from_requirements()
    
    # Test installation
    if test_installation():
        print("\n🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Start the application: python -m uvicorn app.main:app --reload")
        print("2. The system will automatically use MediaPipe + MobileFaceNet")
        print("3. Check logs for initialization messages")
        print("4. Test with your cameras for smooth face recognition")
        return True
    else:
        print("\n⚠️ Installation completed but tests failed")
        print("The system will fall back to ArcFace if MediaPipe doesn't work")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✨ Ready to use MediaPipe + MobileFaceNet for smooth face recognition!")
    else:
        print("\n⚠️ Installation had issues, but the system will still work with fallback models")
    
    sys.exit(0 if success else 1)
