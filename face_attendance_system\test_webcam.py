#!/usr/bin/env python3
"""
Simple webcam test to check if camera is accessible
"""

import cv2
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_webcam_access():
    """Test if webcam is accessible"""
    logger.info("🔄 Testing webcam access...")
    
    # Try different camera indices
    for camera_index in [0, 1, 2]:
        logger.info(f"🔍 Trying camera index {camera_index}...")
        
        cap = cv2.VideoCapture(camera_index)
        
        if cap.isOpened():
            logger.info(f"✅ Camera {camera_index} opened successfully!")
            
            # Try to read a frame
            ret, frame = cap.read()
            if ret and frame is not None:
                logger.info(f"✅ Frame captured successfully from camera {camera_index}")
                logger.info(f"   Frame shape: {frame.shape}")
                logger.info(f"   Frame dtype: {frame.dtype}")
                
                # Test streaming for 5 seconds
                logger.info(f"🎬 Testing streaming for 5 seconds...")
                start_time = time.time()
                frame_count = 0
                
                while time.time() - start_time < 5:
                    ret, frame = cap.read()
                    if ret:
                        frame_count += 1
                        cv2.imshow(f'Webcam Test - Camera {camera_index}', frame)
                        if cv2.waitKey(1) & 0xFF == ord('q'):
                            break
                    else:
                        logger.warning(f"⚠️ Failed to read frame {frame_count}")
                
                cv2.destroyAllWindows()
                cap.release()
                
                logger.info(f"✅ Streaming test completed for camera {camera_index}")
                logger.info(f"   Frames captured: {frame_count}")
                logger.info(f"   Average FPS: {frame_count/5:.1f}")
                
                return camera_index  # Return working camera index
            else:
                logger.error(f"❌ Camera {camera_index} opened but cannot read frames")
                cap.release()
        else:
            logger.warning(f"⚠️ Cannot open camera {camera_index}")
    
    logger.error("❌ No working webcam found!")
    return None

def test_camera_with_settings():
    """Test camera with optimized settings"""
    working_camera = test_webcam_access()
    
    if working_camera is None:
        return False
    
    logger.info(f"🔧 Testing camera {working_camera} with optimized settings...")
    
    cap = cv2.VideoCapture(working_camera)
    
    if not cap.isOpened():
        logger.error(f"❌ Cannot reopen camera {working_camera}")
        return False
    
    # Apply optimized settings
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 2)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    logger.info(f"📷 Camera settings applied:")
    logger.info(f"   Buffer size: {cap.get(cv2.CAP_PROP_BUFFERSIZE)}")
    logger.info(f"   Resolution: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
    logger.info(f"   FPS: {cap.get(cv2.CAP_PROP_FPS)}")
    
    # Test streaming
    logger.info("🎬 Testing optimized streaming for 10 seconds...")
    start_time = time.time()
    frame_count = 0
    
    while time.time() - start_time < 10:
        ret, frame = cap.read()
        if ret and frame is not None:
            frame_count += 1
            
            # Add frame counter text
            cv2.putText(frame, f'Frame: {frame_count}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f'FPS: {frame_count/(time.time()-start_time):.1f}', (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            cv2.imshow(f'Optimized Webcam Test - Camera {working_camera}', frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        else:
            logger.warning(f"⚠️ Failed to read frame {frame_count}")
    
    cv2.destroyAllWindows()
    cap.release()
    
    elapsed_time = time.time() - start_time
    logger.info(f"✅ Optimized streaming test completed:")
    logger.info(f"   Frames captured: {frame_count}")
    logger.info(f"   Average FPS: {frame_count/elapsed_time:.1f}")
    logger.info(f"   Test duration: {elapsed_time:.1f}s")
    
    return frame_count > 0

def main():
    """Main test function"""
    logger.info("🚀 Webcam Streaming Test")
    logger.info("=" * 50)
    
    try:
        success = test_camera_with_settings()
        
        if success:
            logger.info("🎉 Webcam test PASSED!")
            logger.info("✅ Your webcam is working correctly")
            logger.info("🎯 You can now use it in the face attendance system")
        else:
            logger.error("❌ Webcam test FAILED!")
            logger.error("🔧 Troubleshooting steps:")
            logger.error("   1. Check if webcam is connected")
            logger.error("   2. Close other applications using the camera")
            logger.error("   3. Try different camera indices (0, 1, 2)")
            logger.error("   4. Check camera permissions")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    exit(0 if success else 1)
