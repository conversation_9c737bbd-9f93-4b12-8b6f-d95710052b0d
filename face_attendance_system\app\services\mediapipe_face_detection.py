"""
MediaPipe Face Detection Service
High-performance face detection using MediaPipe
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import List, Tuple, Optional
import logging
import time

# Configure logging
logger = logging.getLogger(__name__)

class MediaPipeFaceDetection:
    def __init__(self, min_detection_confidence: float = 0.5, model_selection: int = 0):
        """
        Initialize MediaPipe Face Detection
        
        Args:
            min_detection_confidence: Minimum confidence for face detection (0.0-1.0)
            model_selection: 0 for short-range (2m), 1 for full-range (5m)
        """
        self.min_detection_confidence = min_detection_confidence
        self.model_selection = model_selection
        
        # Initialize MediaPipe Face Detection
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Create face detection instance
        self.face_detection = self.mp_face_detection.FaceDetection(
            min_detection_confidence=min_detection_confidence,
            model_selection=model_selection
        )
        
        # Performance metrics
        self.detection_times = []
        self.total_detections = 0
        
        logger.info(f"✅ MediaPipe Face Detection initialized (model: {model_selection}, confidence: {min_detection_confidence})")
    
    def detect_faces(self, image: np.ndarray) -> List[dict]:
        """
        Detect faces in an image using MediaPipe
        
        Args:
            image: Input image (BGR format)
            
        Returns:
            List of face detection results with bounding boxes and landmarks
        """
        start_time = time.time()
        
        try:
            # Convert BGR to RGB for MediaPipe
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Perform face detection
            results = self.face_detection.process(rgb_image)
            
            faces = []
            if results.detections:
                h, w = image.shape[:2]
                
                for detection in results.detections:
                    # Get bounding box
                    bbox = detection.location_data.relative_bounding_box
                    
                    # Convert relative coordinates to absolute
                    x = int(bbox.xmin * w)
                    y = int(bbox.ymin * h)
                    width = int(bbox.width * w)
                    height = int(bbox.height * h)
                    
                    # Ensure coordinates are within image bounds
                    x = max(0, x)
                    y = max(0, y)
                    width = min(width, w - x)
                    height = min(height, h - y)
                    
                    # Get key points (landmarks)
                    keypoints = []
                    if detection.location_data.relative_keypoints:
                        for keypoint in detection.location_data.relative_keypoints:
                            kp_x = int(keypoint.x * w)
                            kp_y = int(keypoint.y * h)
                            keypoints.append((kp_x, kp_y))
                    
                    face_info = {
                        'bbox': (x, y, width, height),
                        'confidence': detection.score[0],
                        'keypoints': keypoints,
                        'detection': detection  # Keep original detection for drawing
                    }
                    
                    faces.append(face_info)
            
            # Update metrics
            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            self.total_detections += len(faces)
            
            logger.debug(f"🔍 MediaPipe detected {len(faces)} faces in {detection_time*1000:.2f}ms")
            
            return faces
            
        except Exception as e:
            logger.error(f"❌ MediaPipe face detection error: {e}")
            return []
    
    def extract_face_region(self, image: np.ndarray, bbox: Tuple[int, int, int, int], 
                           padding: float = 0.2) -> Optional[np.ndarray]:
        """
        Extract face region from image with padding
        
        Args:
            image: Input image
            bbox: Bounding box (x, y, width, height)
            padding: Padding factor (0.2 = 20% padding)
            
        Returns:
            Cropped face image or None if invalid
        """
        try:
            x, y, width, height = bbox
            h, w = image.shape[:2]
            
            # Add padding
            pad_x = int(width * padding)
            pad_y = int(height * padding)
            
            # Calculate padded coordinates
            x1 = max(0, x - pad_x)
            y1 = max(0, y - pad_y)
            x2 = min(w, x + width + pad_x)
            y2 = min(h, y + height + pad_y)
            
            # Extract face region
            face_region = image[y1:y2, x1:x2]
            
            if face_region.size == 0:
                return None
                
            return face_region
            
        except Exception as e:
            logger.error(f"❌ Face extraction error: {e}")
            return None
    
    def draw_detections(self, image: np.ndarray, faces: List[dict]) -> np.ndarray:
        """
        Draw face detection results on image
        
        Args:
            image: Input image
            faces: List of face detection results
            
        Returns:
            Image with drawn detections
        """
        annotated_image = image.copy()
        
        try:
            # Convert to RGB for MediaPipe drawing
            rgb_image = cv2.cvtColor(annotated_image, cv2.COLOR_BGR2RGB)
            
            for face in faces:
                if 'detection' in face:
                    # Use MediaPipe's drawing utilities
                    self.mp_drawing.draw_detection(rgb_image, face['detection'])
                else:
                    # Fallback: draw bounding box manually
                    x, y, w, h = face['bbox']
                    cv2.rectangle(rgb_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                    
                    # Draw confidence
                    confidence = face.get('confidence', 0.0)
                    cv2.putText(rgb_image, f'{confidence:.2f}', (x, y - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # Convert back to BGR
            annotated_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)
            
        except Exception as e:
            logger.error(f"❌ Drawing error: {e}")
        
        return annotated_image
    
    def get_performance_stats(self) -> dict:
        """Get performance statistics"""
        if not self.detection_times:
            return {
                'avg_detection_time': 0.0,
                'total_detections': 0,
                'fps': 0.0
            }
        
        avg_time = np.mean(self.detection_times)
        fps = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            'avg_detection_time': avg_time * 1000,  # Convert to ms
            'total_detections': self.total_detections,
            'fps': fps
        }
    
    def __del__(self):
        """Cleanup resources"""
        try:
            if hasattr(self, 'face_detection'):
                self.face_detection.close()
        except:
            pass
