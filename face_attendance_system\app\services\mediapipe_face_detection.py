import cv2
import numpy as np
import mediapipe as mp
import time
import logging
from typing import List, Dict, Tuple, Optional

logger = logging.getLogger(__name__)

class MediaPipeFaceDetector:
    """
    MediaPipe Face Detection for fast and accurate face detection
    Optimized for real-time video processing with <5ms detection time
    """
    
    def __init__(self,
                 model_selection: int = 0,  # 0 for short-range (2m), 1 for full-range (5m)
                 min_detection_confidence: float = 0.3):  # Lower threshold for better detection
        """
        Initialize MediaPipe Face Detection
        
        Args:
            model_selection: 0 for short-range model (faster), 1 for full-range model
            min_detection_confidence: Minimum confidence threshold for face detection
        """
        self.model_selection = model_selection
        self.min_detection_confidence = min_detection_confidence
        
        # Initialize MediaPipe Face Detection
        self.mp_face_detection = mp.solutions.face_detection
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Create face detection instance
        self.face_detection = self.mp_face_detection.FaceDetection(
            model_selection=model_selection,
            min_detection_confidence=min_detection_confidence
        )
        
        # Performance metrics
        self.detection_times = []
        self.total_detections = 0
        
        logger.info(f"✅ MediaPipe Face Detection initialized (model: {model_selection}, confidence: {min_detection_confidence})")
    
    def detect_faces(self, frame: np.ndarray) -> List[Dict]:
        """
        Detect faces in frame using MediaPipe
        
        Args:
            frame: Input BGR image frame
            
        Returns:
            List of face detection dictionaries with bounding boxes and confidence
        """
        start_time = time.time()
        
        try:
            # Convert BGR to RGB for MediaPipe
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Perform face detection
            results = self.face_detection.process(rgb_frame)
            
            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            
            faces = []
            
            if results.detections:
                self.total_detections += len(results.detections)
                
                for detection in results.detections:
                    # Get bounding box
                    bbox = detection.location_data.relative_bounding_box
                    
                    # Convert relative coordinates to absolute pixel coordinates
                    h, w, _ = frame.shape
                    x = int(bbox.xmin * w)
                    y = int(bbox.ymin * h)
                    width = int(bbox.width * w)
                    height = int(bbox.height * h)
                    
                    # Ensure coordinates are within frame bounds
                    x = max(0, x)
                    y = max(0, y)
                    width = min(width, w - x)
                    height = min(height, h - y)
                    
                    # Get confidence score
                    confidence = detection.score[0] if detection.score else 0.0
                    
                    face_info = {
                        'bbox': (x, y, width, height),  # (x, y, width, height)
                        'bbox_xyxy': (x, y, x + width, y + height),  # (x1, y1, x2, y2)
                        'confidence': confidence,
                        'detection_time': detection_time
                    }
                    
                    faces.append(face_info)
                    
                logger.debug(f"🔍 MediaPipe detected {len(faces)} face(s) in {detection_time:.3f}s")
            
            return faces
            
        except Exception as e:
            logger.error(f"❌ MediaPipe face detection error: {e}")
            return []
    
    def crop_face(self, frame: np.ndarray, bbox: Tuple[int, int, int, int], 
                  padding: float = 0.2) -> Optional[np.ndarray]:
        """
        Crop face from frame with optional padding
        
        Args:
            frame: Input BGR image frame
            bbox: Bounding box as (x, y, width, height)
            padding: Padding factor (0.2 = 20% padding)
            
        Returns:
            Cropped face image or None if invalid
        """
        try:
            x, y, width, height = bbox
            h, w, _ = frame.shape
            
            # Add padding
            pad_x = int(width * padding)
            pad_y = int(height * padding)
            
            # Calculate padded coordinates
            x1 = max(0, x - pad_x)
            y1 = max(0, y - pad_y)
            x2 = min(w, x + width + pad_x)
            y2 = min(h, y + height + pad_y)
            
            # Crop face
            face_crop = frame[y1:y2, x1:x2]
            
            if face_crop.size == 0:
                logger.warning("⚠️ Empty face crop")
                return None
                
            return face_crop
            
        except Exception as e:
            logger.error(f"❌ Face cropping error: {e}")
            return None
    
    def draw_detections(self, frame: np.ndarray, faces: List[Dict]) -> np.ndarray:
        """
        Draw face detection bounding boxes on frame
        
        Args:
            frame: Input BGR image frame
            faces: List of face detection dictionaries
            
        Returns:
            Frame with drawn bounding boxes
        """
        try:
            annotated_frame = frame.copy()
            
            for face in faces:
                bbox = face['bbox']
                confidence = face['confidence']
                
                x, y, width, height = bbox
                
                # Draw bounding box
                cv2.rectangle(annotated_frame, (x, y), (x + width, y + height), (0, 255, 0), 2)
                
                # Draw confidence score
                label = f"Face: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                cv2.rectangle(annotated_frame, (x, y - label_size[1] - 10), 
                             (x + label_size[0], y), (0, 255, 0), -1)
                cv2.putText(annotated_frame, label, (x, y - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            
            return annotated_frame
            
        except Exception as e:
            logger.error(f"❌ Drawing detections error: {e}")
            return frame
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        if not self.detection_times:
            return {
                'avg_detection_time': 0.0,
                'min_detection_time': 0.0,
                'max_detection_time': 0.0,
                'total_detections': 0,
                'fps_estimate': 0.0
            }
        
        avg_time = np.mean(self.detection_times)
        min_time = np.min(self.detection_times)
        max_time = np.max(self.detection_times)
        fps_estimate = 1.0 / avg_time if avg_time > 0 else 0.0
        
        return {
            'avg_detection_time': avg_time,
            'min_detection_time': min_time,
            'max_detection_time': max_time,
            'total_detections': self.total_detections,
            'fps_estimate': fps_estimate
        }
    
    def __del__(self):
        """Cleanup MediaPipe resources"""
        try:
            if hasattr(self, 'face_detection'):
                self.face_detection.close()
        except:
            pass
