#!/usr/bin/env python3
"""
Add a test camera and start streaming
"""

import requests
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def add_camera():
    """Add a test camera using form data"""
    logger.info("🔄 Adding test camera...")
    
    # Form data for adding camera
    form_data = {
        "name": "Test Webcam",
        "camera_type": "IN",
        "url": "0"  # Webcam index
    }
    
    try:
        response = requests.post(f"{BASE_URL}/manage-camera", data=form_data, allow_redirects=False)
        
        if response.status_code in [200, 302]:  # 302 is redirect after successful add
            logger.info("✅ Camera added successfully")
            return True
        else:
            logger.error(f"❌ Failed to add camera: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error adding camera: {e}")
        return False

def get_cameras():
    """Get list of cameras"""
    logger.info("🔄 Getting camera list...")
    
    try:
        response = requests.get(f"{BASE_URL}/debug-cameras")
        
        if response.status_code == 200:
            data = response.json()
            cameras = data.get("database_cameras", [])
            logger.info(f"✅ Found {len(cameras)} cameras in database")
            
            for camera in cameras:
                logger.info(f"   Camera {camera['id']}: {camera['name']} ({camera['url']})")
            
            return cameras
        else:
            logger.error(f"❌ Failed to get cameras: {response.status_code}")
            return []
            
    except Exception as e:
        logger.error(f"❌ Error getting cameras: {e}")
        return []

def start_camera(camera_id):
    """Start a camera"""
    logger.info(f"🔄 Starting camera {camera_id}...")
    
    try:
        response = requests.post(f"{BASE_URL}/start-camera/{camera_id}")
        
        if response.status_code == 200:
            logger.info("✅ Camera started successfully")
            return True
        else:
            logger.error(f"❌ Failed to start camera: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error starting camera: {e}")
        return False

def test_camera_stream(camera_id):
    """Test camera streaming"""
    logger.info(f"🔄 Testing camera stream for ID: {camera_id}")
    
    try:
        stream_url = f"{BASE_URL}/camera-stream/{camera_id}"
        
        # Test with a short timeout
        response = requests.get(stream_url, stream=True, timeout=5)
        
        if response.status_code == 200:
            logger.info("✅ Camera stream endpoint accessible")
            
            # Read a few chunks
            chunk_count = 0
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    chunk_count += 1
                    if chunk_count >= 3:  # Read 3 chunks
                        break
            
            logger.info(f"✅ Received {chunk_count} chunks from stream")
            return chunk_count > 0
        else:
            logger.error(f"❌ Stream endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing stream: {e}")
        return False

def main():
    """Main function"""
    logger.info("🚀 Camera Setup and Test")
    logger.info("=" * 40)
    
    try:
        # Step 1: Add camera
        if not add_camera():
            logger.error("❌ Failed to add camera")
            return False
        
        # Step 2: Get cameras to find our camera ID
        cameras = get_cameras()
        if not cameras:
            logger.error("❌ No cameras found")
            return False
        
        # Find our test camera
        test_camera = None
        for camera in cameras:
            if camera['name'] == 'Test Webcam':
                test_camera = camera
                break
        
        if not test_camera:
            logger.error("❌ Test camera not found")
            return False
        
        camera_id = test_camera['id']
        logger.info(f"🔍 Found test camera with ID: {camera_id}")
        
        # Step 3: Start camera
        if not start_camera(camera_id):
            logger.error("❌ Failed to start camera")
            return False
        
        # Step 4: Wait for camera to initialize
        logger.info("⏳ Waiting for camera to initialize...")
        time.sleep(3)
        
        # Step 5: Test streaming
        if test_camera_stream(camera_id):
            logger.info("🎉 SUCCESS! Camera is streaming correctly")
            logger.info(f"🌐 You can view the stream at: {BASE_URL}/camera-stream/{camera_id}")
            logger.info(f"🌐 Camera management: {BASE_URL}/manage-camera")
            return True
        else:
            logger.error("❌ Camera streaming test failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 Next steps:")
        print("1. Go to http://localhost:8000/manage-camera")
        print("2. You should see your Test Webcam listed")
        print("3. The camera should be streaming")
        print("4. Register users to test face recognition")
    else:
        print("\n❌ Setup failed. Check the logs above.")
    
    exit(0 if success else 1)
