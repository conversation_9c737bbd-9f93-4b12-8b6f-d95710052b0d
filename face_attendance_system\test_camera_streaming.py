#!/usr/bin/env python3
"""
Test camera streaming functionality
"""

import requests
import time
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

def test_add_camera():
    """Test adding a webcam"""
    logger.info("🔄 Testing camera addition...")
    
    camera_data = {
        "camera_name": "Test Webcam",
        "rtsp_url": "0",  # Webcam
        "camera_type": "IN",
        "location": "Test Location"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/add-camera", data=camera_data)
        
        if response.status_code == 200:
            logger.info("✅ Camera added successfully")
            return True
        else:
            logger.error(f"❌ Failed to add camera: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error adding camera: {e}")
        return False

def test_start_camera():
    """Test starting the camera"""
    logger.info("🔄 Testing camera start...")
    
    try:
        # Get cameras first
        response = requests.get(f"{BASE_URL}/cameras")
        if response.status_code != 200:
            logger.error(f"❌ Failed to get cameras: {response.status_code}")
            return False
        
        cameras = response.json()
        if not cameras:
            logger.error("❌ No cameras found")
            return False
        
        # Find our test camera
        test_camera = None
        for camera in cameras:
            if camera['camera_name'] == 'Test Webcam':
                test_camera = camera
                break
        
        if not test_camera:
            logger.error("❌ Test camera not found")
            return False
        
        camera_id = test_camera['id']
        logger.info(f"🔍 Found test camera with ID: {camera_id}")
        
        # Start the camera
        response = requests.post(f"{BASE_URL}/start-camera/{camera_id}")
        
        if response.status_code == 200:
            logger.info("✅ Camera started successfully")
            return camera_id
        else:
            logger.error(f"❌ Failed to start camera: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error starting camera: {e}")
        return False

def test_camera_stream(camera_id):
    """Test camera streaming"""
    logger.info(f"🔄 Testing camera stream for ID: {camera_id}")
    
    try:
        # Test the streaming endpoint
        stream_url = f"{BASE_URL}/camera-stream/{camera_id}"
        
        response = requests.get(stream_url, stream=True, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ Camera stream endpoint accessible")
            
            # Read a few chunks to verify streaming
            chunk_count = 0
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    chunk_count += 1
                    if chunk_count >= 5:  # Read 5 chunks
                        break
            
            logger.info(f"✅ Received {chunk_count} chunks from stream")
            return True
        else:
            logger.error(f"❌ Stream endpoint failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing stream: {e}")
        return False

def test_minimal_stream():
    """Test the minimal camera stream"""
    logger.info("🔄 Testing minimal camera stream...")
    
    try:
        stream_url = f"{BASE_URL}/minimal-camera-stream/0"
        
        response = requests.get(stream_url, stream=True, timeout=10)
        
        if response.status_code == 200:
            logger.info("✅ Minimal camera stream accessible")
            
            # Read a few chunks
            chunk_count = 0
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    chunk_count += 1
                    if chunk_count >= 5:
                        break
            
            logger.info(f"✅ Received {chunk_count} chunks from minimal stream")
            return True
        else:
            logger.error(f"❌ Minimal stream failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing minimal stream: {e}")
        return False

def test_stop_camera(camera_id):
    """Test stopping the camera"""
    logger.info(f"🔄 Testing camera stop for ID: {camera_id}")
    
    try:
        response = requests.post(f"{BASE_URL}/stop-camera/{camera_id}")
        
        if response.status_code == 200:
            logger.info("✅ Camera stopped successfully")
            return True
        else:
            logger.error(f"❌ Failed to stop camera: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error stopping camera: {e}")
        return False

def cleanup_test_camera():
    """Remove test camera"""
    logger.info("🔄 Cleaning up test camera...")
    
    try:
        # Get cameras
        response = requests.get(f"{BASE_URL}/cameras")
        if response.status_code != 200:
            return
        
        cameras = response.json()
        for camera in cameras:
            if camera['camera_name'] == 'Test Webcam':
                camera_id = camera['id']
                # Stop camera first
                requests.post(f"{BASE_URL}/stop-camera/{camera_id}")
                # Delete camera
                requests.delete(f"{BASE_URL}/delete-camera/{camera_id}")
                logger.info(f"✅ Cleaned up test camera {camera_id}")
                break
                
    except Exception as e:
        logger.error(f"❌ Error cleaning up: {e}")

def main():
    """Main test function"""
    logger.info("🚀 Camera Streaming Test")
    logger.info("=" * 50)
    
    try:
        # Test 1: Minimal stream (direct camera access)
        logger.info("\n📋 Test 1: Minimal Camera Stream")
        logger.info("-" * 30)
        minimal_success = test_minimal_stream()
        
        # Test 2: Full camera system
        logger.info("\n📋 Test 2: Full Camera System")
        logger.info("-" * 30)
        
        # Clean up any existing test camera
        cleanup_test_camera()
        
        # Add camera
        add_success = test_add_camera()
        if not add_success:
            logger.error("❌ Cannot proceed without adding camera")
            return False
        
        # Start camera
        camera_id = test_start_camera()
        if not camera_id:
            logger.error("❌ Cannot proceed without starting camera")
            cleanup_test_camera()
            return False
        
        # Wait a moment for camera to initialize
        logger.info("⏳ Waiting for camera to initialize...")
        time.sleep(3)
        
        # Test streaming
        stream_success = test_camera_stream(camera_id)
        
        # Stop camera
        stop_success = test_stop_camera(camera_id)
        
        # Cleanup
        cleanup_test_camera()
        
        # Results
        logger.info("\n" + "=" * 50)
        logger.info("🏁 Test Results:")
        logger.info(f"   Minimal Stream: {'✅ PASS' if minimal_success else '❌ FAIL'}")
        logger.info(f"   Add Camera: {'✅ PASS' if add_success else '❌ FAIL'}")
        logger.info(f"   Start Camera: {'✅ PASS' if camera_id else '❌ FAIL'}")
        logger.info(f"   Stream Test: {'✅ PASS' if stream_success else '❌ FAIL'}")
        logger.info(f"   Stop Camera: {'✅ PASS' if stop_success else '❌ FAIL'}")
        
        overall_success = minimal_success and add_success and camera_id and stream_success and stop_success
        
        if overall_success:
            logger.info("🎉 All tests PASSED! Camera streaming is working.")
        else:
            logger.error("❌ Some tests FAILED. Check the issues above.")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        cleanup_test_camera()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
