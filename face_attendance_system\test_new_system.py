#!/usr/bin/env python3
"""
Test script for the new MediaPipe + MobileFaceNet face recognition system
"""

import sys
import os
import cv2
import numpy as np
import logging

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mediapipe_detection():
    """Test MediaPipe face detection"""
    try:
        from app.services.mediapipe_face_detection import MediaPipeFaceDetector
        
        logger.info("🔄 Testing MediaPipe Face Detection...")
        
        # Initialize detector
        detector = MediaPipeFaceDetector()
        
        # Create a test image (dummy face-like pattern)
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.circle(test_image, (320, 240), 100, (255, 255, 255), -1)  # Face
        cv2.circle(test_image, (290, 210), 15, (0, 0, 0), -1)  # Left eye
        cv2.circle(test_image, (350, 210), 15, (0, 0, 0), -1)  # Right eye
        cv2.ellipse(test_image, (320, 270), (30, 15), 0, 0, 180, (0, 0, 0), 2)  # Mouth
        
        # Test detection
        faces = detector.detect_faces(test_image)
        
        logger.info(f"✅ MediaPipe detection test completed. Detected {len(faces)} faces")
        
        # Get performance stats
        stats = detector.get_performance_stats()
        logger.info(f"   Performance: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MediaPipe detection test failed: {e}")
        return False

def test_mobilefacenet_recognition():
    """Test MobileFaceNet recognition"""
    try:
        from app.services.mobilefacenet_openvino import MobileFaceNetOpenVINO
        
        logger.info("🔄 Testing MobileFaceNet Recognition...")
        
        # Initialize recognizer
        recognizer = MobileFaceNetOpenVINO()
        
        # Create a test face image
        test_face = np.random.randint(0, 255, (112, 112, 3), dtype=np.uint8)
        
        # Test embedding extraction
        embedding = recognizer.extract_embedding(test_face)
        
        if embedding is not None:
            logger.info(f"✅ MobileFaceNet test completed. Embedding shape: {embedding.shape}")
            
            # Test embedding comparison
            embedding2 = recognizer.extract_embedding(test_face)
            if embedding2 is not None:
                similarity = recognizer.compare_embeddings(embedding, embedding2)
                logger.info(f"   Self-similarity: {similarity:.3f}")
        else:
            logger.warning("⚠️ MobileFaceNet returned None embedding (using dummy mode)")
        
        # Get performance stats
        stats = recognizer.get_performance_stats()
        logger.info(f"   Performance: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MobileFaceNet test failed: {e}")
        return False

def test_unified_system():
    """Test the unified MediaPipe + MobileFaceNet system"""
    try:
        from app.services.mediapipe_mobilefacenet_system import MediaPipeMobileFaceNetSystem
        
        logger.info("🔄 Testing Unified System...")
        
        # Initialize system
        system = MediaPipeMobileFaceNetSystem()
        
        # Create a test image
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.circle(test_image, (320, 240), 100, (255, 255, 255), -1)  # Face
        cv2.circle(test_image, (290, 210), 15, (0, 0, 0), -1)  # Left eye
        cv2.circle(test_image, (350, 210), 15, (0, 0, 0), -1)  # Right eye
        cv2.ellipse(test_image, (320, 270), (30, 15), 0, 0, 180, (0, 0, 0), 2)  # Mouth
        
        # Test recognition
        results = system.recognize_faces_in_frame(test_image)
        
        logger.info(f"✅ Unified system test completed. Results: {len(results)} faces")
        for i, result in enumerate(results):
            logger.info(f"   Face {i+1}: {result['name']} (confidence: {result['confidence']:.3f})")
        
        # Get performance stats
        stats = system.get_performance_stats()
        logger.info(f"   Performance: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Unified system test failed: {e}")
        return False

def test_face_utils_integration():
    """Test the face_utils integration"""
    try:
        from app.services.face_utils import FaceRecognitionSystem
        
        logger.info("🔄 Testing Face Utils Integration...")
        
        # Initialize with MediaPipe + MobileFaceNet
        system = FaceRecognitionSystem(model_name="mediapipe_mobilefacenet")
        
        # Create a test image
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        cv2.circle(test_image, (320, 240), 100, (255, 255, 255), -1)  # Face
        cv2.circle(test_image, (290, 210), 15, (0, 0, 0), -1)  # Left eye
        cv2.circle(test_image, (350, 210), 15, (0, 0, 0), -1)  # Right eye
        cv2.ellipse(test_image, (320, 270), (30, 15), 0, 0, 180, (0, 0, 0), 2)  # Mouth
        
        # Test recognition
        results = system.recognize_faces_in_frame(test_image)
        
        logger.info(f"✅ Face utils integration test completed. Results: {len(results)} faces")
        
        # Test drawing
        annotated_frame = system.draw_face_boxes(test_image, results)
        logger.info(f"   Annotated frame shape: {annotated_frame.shape}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Face utils integration test failed: {e}")
        import traceback
        logger.error(f"   Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run all tests"""
    logger.info("🚀 Starting MediaPipe + MobileFaceNet System Tests")
    logger.info("=" * 60)
    
    tests = [
        ("MediaPipe Detection", test_mediapipe_detection),
        ("MobileFaceNet Recognition", test_mobilefacenet_recognition),
        ("Unified System", test_unified_system),
        ("Face Utils Integration", test_face_utils_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        logger.info("-" * 40)
        
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} PASSED")
        else:
            logger.error(f"❌ {test_name} FAILED")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready for integration.")
        return True
    else:
        logger.error(f"⚠️ {total - passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
